"""
角度阻尼优化器 - 专门解决水中物体姿态恢复问题
Angular Damping Optimizer - Specialized for underwater attitude recovery

解决问题：
1. PhysX错误：PxArticulationLink::addTorque: force is not valid
2. 角度恢复速度过慢
3. 让浮心-重心自然恢复力矩发挥主导作用
"""

import numpy as np
from typing import Dict, Tuple, Optional
import math

class AngularDampingOptimizer:
    """角度阻尼优化器"""
    
    def __init__(self, mass: float, inertia: list, characteristic_length: float):
        """
        初始化角度阻尼优化器
        
        Args:
            mass: 物体质量 (kg)
            inertia: 对角惯性矩 [Ixx, Iyy, Izz] (kg·m²)
            characteristic_length: 特征长度 (m)
        """
        self.mass = mass
        self.inertia = np.array(inertia)
        self.char_length = characteristic_length
        
        # 🌊 水动力学参数
        self.fluid_density = 1000.0  # 水密度 kg/m³
        
        # 🎯 优化策略：最小化人工阻尼，最大化自然恢复
        self.optimization_config = {
            'natural_recovery_priority': True,    # 优先使用自然恢复力矩
            'minimal_artificial_damping': True,   # 最小化人工阻尼
            'axis_specific_tuning': True,         # 轴向特定调优
            'safety_limits': True,                # 安全限制
        }
        
        # 🔧 轴向阻尼系数（基于物理原理）
        self.axis_damping_factors = {
            'pitch': 0.3,    # 俯仰：最小阻尼，让浮心-重心恢复力矩主导
            'roll': 0.3,     # 横滚：最小阻尼，让浮心-重心恢复力矩主导  
            'yaw': 0.6,      # 偏航：稍大阻尼，因为没有自然恢复力矩
        }
        
        print(f"🎯 角度阻尼优化器初始化完成")
        print(f"  策略：最小化人工阻尼，最大化自然恢复")
        print(f"  轴向系数：俯仰={self.axis_damping_factors['pitch']}, "
              f"横滚={self.axis_damping_factors['roll']}, "
              f"偏航={self.axis_damping_factors['yaw']}")
    
    def calculate_optimal_angular_damping(self, angular_velocity: np.ndarray, 
                                        submerged_ratio: float = 1.0) -> Dict[str, np.ndarray]:
        """
        计算优化的角度阻尼
        
        Args:
            angular_velocity: 角速度 [wx, wy, wz] (rad/s)
            submerged_ratio: 淹没比例 (0-1)
            
        Returns:
            Dict包含优化的角阻尼参数
        """
        angular_speed = np.linalg.norm(angular_velocity)
        
        # 🎯 基础角阻尼系数（大幅减少）
        base_angular_damping = self._calculate_minimal_base_damping()
        
        # 🔄 速度自适应调整
        velocity_factor = self._get_velocity_adaptive_factor(angular_speed)
        
        # 🎯 轴向差异化处理
        axis_factors = np.array([
            self.axis_damping_factors['pitch'],   # X轴：俯仰
            self.axis_damping_factors['roll'],    # Y轴：横滚
            self.axis_damping_factors['yaw']      # Z轴：偏航
        ])
        
        # 🌊 计算最终角阻尼系数
        optimized_angular_damping = base_angular_damping * velocity_factor * axis_factors * submerged_ratio
        
        # 🛡️ 安全限制
        optimized_angular_damping = self._apply_safety_limits(optimized_angular_damping)
        
        return {
            'angular_damping_coefficients': optimized_angular_damping,
            'base_damping': base_angular_damping,
            'velocity_factor': velocity_factor,
            'axis_factors': axis_factors,
            'angular_speed': angular_speed
        }
    
    def _calculate_minimal_base_damping(self) -> float:
        """计算最小基础角阻尼系数"""
        # 🎯 基于惯性矩的最小阻尼（大幅减少）
        avg_inertia = np.mean(self.inertia)
        
        # 最小阻尼：仅用于防止高频震荡
        minimal_damping = avg_inertia * 0.5  # 🔧 从原来的2.0-3.0减少到0.5
        
        # 确保不会太小，避免数值问题
        minimal_damping = max(minimal_damping, 0.1)
        
        return minimal_damping
    
    def _get_velocity_adaptive_factor(self, angular_speed: float) -> float:
        """
        基于角速度的自适应因子
        
        低速时：最小阻尼，让自然恢复力矩主导
        高速时：适度增加阻尼，防止过度旋转
        """
        if angular_speed < 0.1:      # 极低速：最小阻尼
            return 0.2
        elif angular_speed < 0.5:    # 低速：小阻尼
            return 0.4
        elif angular_speed < 1.0:    # 中速：适中阻尼
            return 0.7
        elif angular_speed < 2.0:    # 高速：较大阻尼
            return 1.0
        else:                        # 极高速：最大阻尼
            return 1.2
    
    def _apply_safety_limits(self, damping_coeffs: np.ndarray) -> np.ndarray:
        """应用安全限制"""
        # 🛡️ 最小值限制（防止数值问题）
        min_damping = 0.01
        damping_coeffs = np.maximum(damping_coeffs, min_damping)
        
        # 🛡️ 最大值限制（防止PhysX错误）
        max_damping = self.mass * 5.0  # 保守的上限
        damping_coeffs = np.minimum(damping_coeffs, max_damping)
        
        return damping_coeffs
    
    def calculate_safe_angular_torque(self, angular_velocity: np.ndarray,
                                    angular_damping_coeffs: np.ndarray) -> np.ndarray:
        """
        计算安全的角阻尼力矩
        
        Args:
            angular_velocity: 角速度
            angular_damping_coeffs: 角阻尼系数
            
        Returns:
            安全的角阻尼力矩
        """
        angular_speed = np.linalg.norm(angular_velocity)
        
        if angular_speed < 1e-6:
            return np.zeros_like(angular_velocity)
        
        # 🎯 简化的阻尼力矩计算（避免复杂的二次项）
        # 主要使用线性阻尼，减少数值不稳定性
        angular_torque = -angular_damping_coeffs * angular_velocity
        
        # 🛡️ 安全检查
        angular_torque = self._validate_torque_safety(angular_torque)
        
        return angular_torque
    
    def _validate_torque_safety(self, torque: np.ndarray) -> np.ndarray:
        """验证力矩安全性"""
        # 🛡️ 检查NaN和inf
        if np.any(np.isnan(torque)) or np.any(np.isinf(torque)):
            print(f"⚠️  检测到无效力矩值，已重置为零")
            return np.zeros_like(torque)
        
        # 🛡️ 限制力矩大小
        max_torque = self.mass * 10.0  # 保守限制
        torque_magnitude = np.linalg.norm(torque)
        
        if torque_magnitude > max_torque:
            if torque_magnitude > 1e-10:
                torque = torque * (max_torque / torque_magnitude)
                print(f"⚠️  力矩已限制到安全范围: {max_torque:.2f}")
            else:
                torque = np.zeros_like(torque)
        
        return torque
    
    def get_optimization_status(self) -> Dict:
        """获取优化状态"""
        return {
            'optimizer_type': 'angular_damping_optimizer',
            'strategy': 'minimal_artificial_damping_with_natural_recovery',
            'axis_damping_factors': self.axis_damping_factors,
            'optimization_config': self.optimization_config,
            'mass': self.mass,
            'inertia': self.inertia.tolist(),
            'characteristic_length': self.char_length
        }


def create_angular_damping_optimizer(mass: float, inertia: list, 
                                   characteristic_length: float) -> AngularDampingOptimizer:
    """
    创建角度阻尼优化器
    
    Args:
        mass: 质量 (kg)
        inertia: 惯性矩 [Ixx, Iyy, Izz] (kg·m²)
        characteristic_length: 特征长度 (m)
    
    Returns:
        AngularDampingOptimizer 实例
    """
    return AngularDampingOptimizer(mass, inertia, characteristic_length)


# 🧪 测试函数
def test_angular_damping_optimizer():
    """测试角度阻尼优化器"""
    print("🧪 测试角度阻尼优化器...")
    
    # 创建优化器
    optimizer = create_angular_damping_optimizer(
        mass=1.5,
        inertia=[0.1, 0.1, 0.05],
        characteristic_length=0.5
    )
    
    # 测试不同角速度下的阻尼计算
    test_angular_velocities = [
        np.array([0.01, 0.01, 0.01]),  # 极低速
        np.array([0.1, 0.1, 0.1]),     # 低速
        np.array([0.5, 0.5, 0.5]),     # 中速
        np.array([1.0, 1.0, 1.0]),     # 高速
    ]
    
    for i, angular_vel in enumerate(test_angular_velocities):
        result = optimizer.calculate_optimal_angular_damping(angular_vel)
        torque = optimizer.calculate_safe_angular_torque(
            angular_vel, result['angular_damping_coefficients']
        )
        
        print(f"\n测试 {i+1}: 角速度 = {angular_vel}")
        print(f"  阻尼系数: {result['angular_damping_coefficients']}")
        print(f"  阻尼力矩: {torque}")
        print(f"  速度因子: {result['velocity_factor']:.2f}")
    
    print("\n✅ 角度阻尼优化器测试完成")


if __name__ == "__main__":
    test_angular_damping_optimizer()
