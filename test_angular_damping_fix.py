#!/usr/bin/env python3
"""
角度阻尼修复测试脚本
Test script for angular damping fixes

测试目标：
1. 验证PhysX错误是否修复
2. 测试角度恢复速度是否合理
3. 验证浮心-重心自然恢复力矩是否正常工作
"""

import numpy as np
from angular_damping_optimizer import create_angular_damping_optimizer
from hydrodynamic_damping_system import create_hydrodynamic_damping_system

def test_angular_damping_safety():
    """测试角度阻尼的安全性"""
    print("🧪 测试角度阻尼安全性...")
    
    # 创建优化器
    optimizer = create_angular_damping_optimizer(
        mass=1.5,
        inertia=[0.1, 0.1, 0.05],
        characteristic_length=0.5
    )
    
    # 测试极端情况
    extreme_test_cases = [
        ("零角速度", np.array([0.0, 0.0, 0.0])),
        ("极小角速度", np.array([1e-8, 1e-8, 1e-8])),
        ("正常角速度", np.array([0.1, 0.1, 0.1])),
        ("高角速度", np.array([1.0, 1.0, 1.0])),
        ("极高角速度", np.array([10.0, 10.0, 10.0])),
        ("不平衡角速度", np.array([0.01, 1.0, 0.1])),
    ]
    
    print("\n📊 安全性测试结果:")
    for test_name, angular_vel in extreme_test_cases:
        try:
            result = optimizer.calculate_optimal_angular_damping(angular_vel)
            torque = optimizer.calculate_safe_angular_torque(
                angular_vel, result['angular_damping_coefficients']
            )
            
            # 检查是否有无效值
            has_nan = np.any(np.isnan(torque))
            has_inf = np.any(np.isinf(torque))
            torque_magnitude = np.linalg.norm(torque)
            
            status = "✅ 安全" if not (has_nan or has_inf) else "❌ 危险"
            
            print(f"  {test_name}: {status}")
            print(f"    角速度: {angular_vel}")
            print(f"    力矩大小: {torque_magnitude:.4f}")
            print(f"    NaN检查: {'❌' if has_nan else '✅'}")
            print(f"    Inf检查: {'❌' if has_inf else '✅'}")
            
        except Exception as e:
            print(f"  {test_name}: ❌ 异常 - {e}")
    
    print("\n✅ 角度阻尼安全性测试完成")

def test_natural_recovery_simulation():
    """模拟自然恢复过程"""
    print("\n🌊 模拟自然恢复过程...")
    
    # 创建优化器
    optimizer = create_angular_damping_optimizer(
        mass=1.5,
        inertia=[0.1, 0.1, 0.05],
        characteristic_length=0.5
    )
    
    # 模拟物体从倾斜状态恢复到平衡状态
    print("\n📈 恢复过程模拟:")
    print("假设：物体初始倾斜30度，浮心-重心产生自然恢复力矩")
    
    # 初始角速度（由自然恢复力矩产生）
    initial_angular_velocity = np.array([0.2, 0.1, 0.05])  # rad/s
    
    # 模拟时间步长
    dt = 0.1  # 秒
    simulation_time = 5.0  # 总模拟时间
    steps = int(simulation_time / dt)
    
    angular_velocity = initial_angular_velocity.copy()
    
    print(f"时间(s)\t角速度大小(rad/s)\t俯仰阻尼\t横滚阻尼\t偏航阻尼")
    print("-" * 70)
    
    for step in range(steps):
        time = step * dt
        
        # 计算当前阻尼
        result = optimizer.calculate_optimal_angular_damping(angular_velocity)
        damping_coeffs = result['angular_damping_coefficients']
        
        # 计算阻尼力矩
        damping_torque = optimizer.calculate_safe_angular_torque(angular_velocity, damping_coeffs)
        
        # 简化的动力学更新（忽略惯性效应）
        # 实际中还会有浮心-重心恢复力矩
        angular_acceleration = damping_torque / np.array([0.1, 0.1, 0.05])  # T/I
        angular_velocity += angular_acceleration * dt
        
        # 添加一些自然恢复效应（模拟浮心-重心力矩）
        natural_recovery = -angular_velocity * 0.1  # 简化的恢复力矩
        angular_velocity += natural_recovery * dt
        
        angular_speed = np.linalg.norm(angular_velocity)
        
        if step % 5 == 0:  # 每0.5秒输出一次
            print(f"{time:.1f}\t\t{angular_speed:.4f}\t\t{damping_coeffs[0]:.3f}\t\t{damping_coeffs[1]:.3f}\t\t{damping_coeffs[2]:.3f}")
    
    final_angular_speed = np.linalg.norm(angular_velocity)
    print(f"\n📊 恢复结果:")
    print(f"  初始角速度: {np.linalg.norm(initial_angular_velocity):.4f} rad/s")
    print(f"  最终角速度: {final_angular_speed:.4f} rad/s")
    print(f"  恢复效率: {(1 - final_angular_speed/np.linalg.norm(initial_angular_velocity))*100:.1f}%")
    
    if final_angular_speed < 0.01:
        print("✅ 恢复效果良好：角速度已降至很低水平")
    elif final_angular_speed < 0.05:
        print("🔶 恢复效果一般：角速度仍有一定水平")
    else:
        print("❌ 恢复效果不佳：角速度仍然较高")

def compare_damping_systems():
    """比较不同阻尼系统的效果"""
    print("\n🔄 比较不同阻尼系统...")
    
    # 创建优化器和传统系统
    optimizer = create_angular_damping_optimizer(
        mass=1.5,
        inertia=[0.1, 0.1, 0.05],
        characteristic_length=0.5
    )
    
    hydro_system = create_hydrodynamic_damping_system(
        mass=1.5,
        inertia=[0.1, 0.1, 0.05],
        characteristic_length=0.5
    )
    
    # 测试角速度
    test_angular_velocity = np.array([0.5, 0.3, 0.2])
    
    print(f"\n📊 系统比较 (角速度: {test_angular_velocity}):")
    print("-" * 60)
    
    # 优化器结果
    opt_result = optimizer.calculate_optimal_angular_damping(test_angular_velocity)
    opt_torque = optimizer.calculate_safe_angular_torque(
        test_angular_velocity, opt_result['angular_damping_coefficients']
    )
    
    print("🎯 角度阻尼优化器:")
    print(f"  阻尼系数: {opt_result['angular_damping_coefficients']}")
    print(f"  阻尼力矩: {opt_torque}")
    print(f"  力矩大小: {np.linalg.norm(opt_torque):.4f}")
    
    # 水动力学系统结果
    hydro_result = hydro_system.calculate_hydrodynamic_damping(
        np.array([0.1, 0.1, 0.1]),  # 线速度
        test_angular_velocity
    )
    
    print("\n🌊 水动力学阻尼系统:")
    print(f"  角阻尼系数: {hydro_result['angular_damping']}")
    print(f"  角二次阻尼: {hydro_result['angular_quadratic_damping']}")
    
    # 计算水动力学系统的力矩
    hydro_torque = -(hydro_result['angular_damping'] * test_angular_velocity +
                     hydro_result['angular_quadratic_damping'] * 
                     np.linalg.norm(test_angular_velocity) * 
                     (test_angular_velocity / np.linalg.norm(test_angular_velocity)))
    
    print(f"  阻尼力矩: {hydro_torque}")
    print(f"  力矩大小: {np.linalg.norm(hydro_torque):.4f}")
    
    # 比较分析
    opt_magnitude = np.linalg.norm(opt_torque)
    hydro_magnitude = np.linalg.norm(hydro_torque)
    
    print(f"\n📈 比较分析:")
    print(f"  优化器力矩大小: {opt_magnitude:.4f}")
    print(f"  水动力学力矩大小: {hydro_magnitude:.4f}")
    print(f"  优化器相对减少: {(1 - opt_magnitude/hydro_magnitude)*100:.1f}%")
    
    if opt_magnitude < hydro_magnitude:
        print("✅ 优化器成功减少了角阻尼，有利于自然恢复")
    else:
        print("⚠️  优化器未能减少角阻尼")

def main():
    """主测试函数"""
    print("🧪 角度阻尼修复测试开始")
    print("=" * 60)
    
    # 运行所有测试
    test_angular_damping_safety()
    test_natural_recovery_simulation()
    compare_damping_systems()
    
    print("\n" + "=" * 60)
    print("🎉 角度阻尼修复测试完成")
    print("\n📋 修复总结:")
    print("1. ✅ 添加了安全检查，防止PhysX错误")
    print("2. ✅ 大幅减少角阻尼系数，让自然恢复力矩主导")
    print("3. ✅ 实现了轴向差异化阻尼（俯仰/横滚 < 偏航）")
    print("4. ✅ 添加了力矩大小限制和NaN/Inf检查")
    print("5. ✅ 提供了平滑的速度自适应调整")

if __name__ == "__main__":
    main()
